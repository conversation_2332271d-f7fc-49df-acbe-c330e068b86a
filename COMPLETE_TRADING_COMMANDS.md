# 🚀 Complete AstroA Paper Trading Commands

## 📁 Step 1: Navigate to Directory & Activate Environment

```bash
# Navigate to your AstroA directory
cd /home/<USER>/axmadcodes/AstroA

# Activate the virtual environment
source venv/bin/activate

# Verify you're in the right environment (should show (venv) in prompt)
which python
```

## 🎯 Step 2: Quick System Verification

```bash
# Test that everything is working
python -c "from ml_models import TENSORFLOW_AVAILABLE; print('✅ System Ready!')"

# Test configuration
python run_paper_trading.py --test-config
```

## 🚀 Step 3: Start Paper Trading

### Option A: Quick Demo (5 minutes)
```bash
python run_demo_paper_trading.py --duration 5
```

### Option B: Enhanced Session (1 hour) - RECOMMENDED
```bash
python run_enhanced_paper_trading.py --duration 1
```

### Option C: Full Day Trading (8 hours)
```bash
python run_enhanced_paper_trading.py --duration 8
```

### Option D: Unlimited Session (until you stop it)
```bash
python run_enhanced_paper_trading.py
```

### Option E: Background Trading (runs in background)
```bash
# Start in background
nohup python run_enhanced_paper_trading.py --duration 4 > trading_session.log 2>&1 &

# Get the process ID (note it down)
echo $! > trading_pid.txt
```

## 🛑 Step 4: Stop Paper Trading

### If Running in Foreground:
```bash
# Simply press Ctrl+C in the terminal where it's running
```

### If Running in Background:
```bash
# Method 1: Using the saved PID
kill $(cat trading_pid.txt)

# Method 2: Find and kill the process
ps aux | grep "run_enhanced_paper_trading"
kill [PID_NUMBER]

# Method 3: Kill all Python trading processes
pkill -f "run_enhanced_paper_trading"
```

## 📊 Step 5: Live Monitoring (While Trading is Running)

### Real-time Log Monitoring:
```bash
# Monitor live logs (in a new terminal)
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
tail -f logs/enhanced_paper_trading_$(date +%Y%m%d).log
```

### Live Performance Monitoring:
```bash
# In a new terminal
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
python monitor_paper_trading.py
```

### Check Trading Status:
```bash
# Quick status check
python check_trading_status.py
```

### Live Portfolio Monitoring:
```bash
# Watch portfolio changes in real-time
watch -n 30 'python -c "
import json
from pathlib import Path
files = list(Path(\"data\").glob(\"*paper_trading_report*.json\"))
if files:
    with open(max(files)) as f:
        data = json.load(f)
        print(f\"💰 Portfolio Value: \${data.get(\"final_portfolio_value\", 0):,.2f}\")
        print(f\"📈 Total Return: {data.get(\"total_return_percentage\", 0):.2f}%\")
        print(f\"📊 Total Trades: {data.get(\"total_trades\", 0)}\")
else:
    print(\"No trading data yet...\")
"'
```

## 🔄 Complete Session Example

Here's a complete example of starting a 2-hour trading session with monitoring:

### Terminal 1 (Main Trading):
```bash
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
python run_enhanced_paper_trading.py --duration 2
```

### Terminal 2 (Live Monitoring):
```bash
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
tail -f logs/enhanced_paper_trading_$(date +%Y%m%d).log
```

### Terminal 3 (Performance Monitoring):
```bash
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
watch -n 60 'python monitor_paper_trading.py'
```

## 🎯 Recommended First Run

```bash
# Terminal 1: Start trading
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
python run_enhanced_paper_trading.py --duration 1

# Terminal 2: Monitor logs (open in new terminal)
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
tail -f logs/enhanced_paper_trading_$(date +%Y%m%d).log
```

## 📱 One-Line Commands for Quick Access

### Start 1-Hour Session:
```bash
cd /home/<USER>/axmadcodes/AstroA && source venv/bin/activate && python run_enhanced_paper_trading.py --duration 1
```

### Start Background Session:
```bash
cd /home/<USER>/axmadcodes/AstroA && source venv/bin/activate && nohup python run_enhanced_paper_trading.py --duration 4 > trading.log 2>&1 & echo $! > trading_pid.txt
```

### Monitor Live:
```bash
cd /home/<USER>/axmadcodes/AstroA && source venv/bin/activate && tail -f logs/enhanced_paper_trading_$(date +%Y%m%d).log
```

### Stop Background Session:
```bash
cd /home/<USER>/axmadcodes/AstroA && kill $(cat trading_pid.txt)
```

## 📊 What You'll See During Trading

### Startup Output:
```
🌟 Starting AstroA Paper Trading Session
==================================================
💰 Initial Cash: $100,000.00
📊 Max Positions: 10
🎯 Tradable Symbols: BTCUSD, ETHUSD, ADAUSD, SOLUSD, DOTUSD...
⚡ Update Interval: 60s
==================================================
📅 Session duration: 1 hours
🕐 Session will end at: 15:30:45

✅ ML models imported successfully
🔧 LSTM Predictor: PyTorch-based
🔍 Pattern Recognizer: Fallback mode
🚀 Starting live trading session...
```

### Live Trading Output:
```
📊 Trading iteration 1
✅ Got live data from Binance for 8 symbols
🧠 ML Analysis: BTCUSD - Bullish signal (confidence: 0.73)
💰 BUY BTCUSD: 0.05 units at $43,250.00
📈 Portfolio Value: $100,125.50 (+0.13%)
```

### Results Location:
- **Trading Results**: `data/enhanced_paper_trading_report_[timestamp].json`
- **Detailed Logs**: `logs/enhanced_paper_trading_[date].log`
- **Performance Reports**: `reports/`

## 🆘 Emergency Commands

### If System Hangs:
```bash
# Force kill all trading processes
pkill -f "python.*trading"
```

### If Need to Reset:
```bash
cd /home/<USER>/axmadcodes/AstroA
source venv/bin/activate
python -c "print('System check...'); from ml_models import *; print('✅ Ready!')"
```

Your system is ready! Start with the 1-hour enhanced session to see everything in action! 🚀
