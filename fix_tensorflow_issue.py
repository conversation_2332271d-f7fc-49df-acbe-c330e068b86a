#!/usr/bin/env python3
"""
Fix TensorFlow compatibility issues for AstroA Trading System
This script patches TensorFlow imports to prevent crashes on incompatible CPUs
"""

import os
import sys
import shutil
from pathlib import Path

def backup_file(file_path):
    """Create a backup of the original file"""
    backup_path = f"{file_path}.backup"
    if not os.path.exists(backup_path):
        shutil.copy2(file_path, backup_path)
        print(f"✅ Backed up {file_path}")

def patch_tensorflow_imports():
    """Patch TensorFlow imports in ML model files"""
    
    # Files that need patching
    files_to_patch = [
        'ml_models/lstm_predictor.py',
        'ml_models/pattern_recognizer.py'
    ]
    
    tensorflow_import_patch = '''
# TensorFlow compatibility patch
try:
    import tensorflow as tf
    # Test if Tensor<PERSON>low works
    test_tensor = tf.constant([1.0])
    TENSORFLOW_WORKING = True
except Exception:
    TENSORFLOW_WORKING = False
    # Create mock TensorFlow objects
    class MockTF:
        class keras:
            class models:
                @staticmethod
                def Sequential():
                    return None
            class layers:
                @staticmethod
                def LSTM(*args, **kwargs):
                    return None
                @staticmethod
                def Dense(*args, **kwargs):
                    return None
                @staticmethod
                def Dropout(*args, **kwargs):
                    return None
                @staticmethod
                def Conv1D(*args, **kwargs):
                    return None
                @staticmethod
                def MaxPooling1D(*args, **kwargs):
                    return None
                @staticmethod
                def Flatten(*args, **kwargs):
                    return None
                @staticmethod
                def BatchNormalization(*args, **kwargs):
                    return None
            class optimizers:
                @staticmethod
                def Adam(*args, **kwargs):
                    return None
            class callbacks:
                @staticmethod
                def EarlyStopping(*args, **kwargs):
                    return None
                @staticmethod
                def ReduceLROnPlateau(*args, **kwargs):
                    return None
    
    tf = MockTF()
'''
    
    for file_path in files_to_patch:
        if os.path.exists(file_path):
            print(f"🔧 Patching {file_path}...")
            
            # Backup original
            backup_file(file_path)
            
            # Read original content
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Replace TensorFlow imports
            if 'import tensorflow as tf' in content:
                content = content.replace(
                    'import tensorflow as tf',
                    tensorflow_import_patch
                )
                
                # Add fallback logic for model creation
                if 'lstm_predictor.py' in file_path:
                    content = content.replace(
                        'self.model = self._build_model()',
                        '''if TENSORFLOW_WORKING:
                    self.model = self._build_model()
                else:
                    self.logger.warning("TensorFlow not working, using fallback predictor")
                    from .tensorflow_fallback import FallbackLSTMPredictor
                    self.fallback_model = FallbackLSTMPredictor(self.sequence_length, self.prediction_horizon)
                    self.model = None'''
                    )
                
                # Write patched content
                with open(file_path, 'w') as f:
                    f.write(content)
                
                print(f"✅ Patched {file_path}")
            else:
                print(f"⚠️  No TensorFlow imports found in {file_path}")
        else:
            print(f"❌ File not found: {file_path}")

def create_safe_ml_wrapper():
    """Create a safe ML wrapper that handles TensorFlow issues"""
    
    wrapper_content = '''"""
Safe ML Model Wrapper
Handles TensorFlow compatibility issues gracefully
"""

import logging
import warnings
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class SafeMLWrapper:
    """Wrapper that safely handles ML model operations"""
    
    def __init__(self, model_class, model_id: str, **kwargs):
        self.model_class = model_class
        self.model_id = model_id
        self.kwargs = kwargs
        self.model = None
        self.fallback_active = False
        
        try:
            self.model = model_class(model_id, **kwargs)
            logger.info(f"Successfully initialized {model_class.__name__}")
        except Exception as e:
            logger.warning(f"Failed to initialize {model_class.__name__}: {e}")
            self.fallback_active = True
            self._setup_fallback()
    
    def _setup_fallback(self):
        """Setup fallback functionality"""
        from .tensorflow_fallback import FallbackLSTMPredictor
        
        if 'LSTM' in self.model_class.__name__:
            self.model = FallbackLSTMPredictor(**self.kwargs)
            logger.info("Using fallback LSTM predictor")
        else:
            # Generic fallback
            self.model = self._create_generic_fallback()
            logger.info("Using generic fallback model")
    
    def _create_generic_fallback(self):
        """Create a generic fallback model"""
        class GenericFallback:
            def __init__(self):
                self.is_trained = False
            
            async def train(self, training_data, validation_data=None, **kwargs):
                logger.info("Fallback model training (no-op)")
                self.is_trained = True
                return {"status": "success", "message": "Fallback training completed"}
            
            async def predict(self, data, **kwargs):
                logger.info("Fallback model prediction")
                if isinstance(data, pd.DataFrame):
                    n_samples = len(data)
                else:
                    n_samples = len(data) if hasattr(data, '__len__') else 1
                
                # Return neutral predictions
                return {
                    "predictions": [0.5] * n_samples,
                    "confidence": 0.5,
                    "fallback": True
                }
        
        return GenericFallback()
    
    async def train(self, *args, **kwargs):
        """Safe training wrapper"""
        try:
            if hasattr(self.model, 'train'):
                return await self.model.train(*args, **kwargs)
            else:
                return {"status": "success", "message": "Fallback training"}
        except Exception as e:
            logger.error(f"Training error: {e}")
            return {"status": "error", "message": str(e)}
    
    async def predict(self, *args, **kwargs):
        """Safe prediction wrapper"""
        try:
            if hasattr(self.model, 'predict'):
                return await self.model.predict(*args, **kwargs)
            else:
                return {"predictions": [0.5], "confidence": 0.5, "fallback": True}
        except Exception as e:
            logger.error(f"Prediction error: {e}")
            return {"predictions": [0.5], "confidence": 0.5, "error": str(e)}

def create_safe_model(model_class, model_id: str, **kwargs):
    """Factory function to create safe ML models"""
    return SafeMLWrapper(model_class, model_id, **kwargs)
'''
    
    with open('ml_models/safe_wrapper.py', 'w') as f:
        f.write(wrapper_content)
    
    print("✅ Created safe ML wrapper")

def main():
    """Main function to fix TensorFlow issues"""
    print("🔧 AstroA TensorFlow Compatibility Fix")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('ml_models'):
        print("❌ Error: ml_models directory not found. Run this script from the AstroA root directory.")
        sys.exit(1)
    
    # Patch TensorFlow imports
    patch_tensorflow_imports()
    
    # Create safe wrapper
    create_safe_ml_wrapper()
    
    print("\n✅ TensorFlow compatibility fixes applied!")
    print("\n📋 Next steps:")
    print("1. Test the ML models: python -c 'from ml_models import TENSORFLOW_AVAILABLE; print(f\"TF Available: {TENSORFLOW_AVAILABLE}\")'")
    print("2. Run paper trading: python run_paper_trading.py --test-config")
    print("3. If issues persist, the system will use PyTorch/scikit-learn fallbacks")

if __name__ == "__main__":
    main()
