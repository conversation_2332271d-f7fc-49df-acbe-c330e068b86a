"""
LSTM Neural Network Predictor for Time Series Forecasting
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from typing import Dict, List, Tuple, Any, Optional
import logging

from .base_model import BaseMLModel

class LSTMPredictor(BaseMLModel):
    """LSTM Neural Network for price and trend prediction"""

    def __init__(self, model_id: str, sequence_length: int = 60, prediction_horizon: int = 1):
        super().__init__(model_id, "LSTM", "1.0")

        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.target_scaler = MinMaxScaler(feature_range=(0, 1))

        # Model hyperparameters
        self.hyperparameters = {
            'lstm_units': [50, 50, 25],
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 100,
            'patience': 15
        }

        self.metadata['hyperparameters'] = self.hyperparameters

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train the LSTM model"""
        try:
            self.logger.info(f"Starting LSTM training with {len(training_data)} samples")

            # Update hyperparameters with any provided kwargs
            self.hyperparameters.update(kwargs)

            # Prepare training data
            X_train, y_train = self._prepare_sequences(training_data, is_training=True)

            if X_train is None or len(X_train) == 0:
                raise ValueError("Insufficient training data for sequence creation")

            # Prepare validation data if provided
            X_val, y_val = None, None
            if validation_data is not None:
                X_val, y_val = self._prepare_sequences(validation_data, is_training=False)

            # Build and compile model
            self.model = self._build_model(X_train.shape[1], X_train.shape[2])

            # Setup callbacks
            callbacks = [
                EarlyStopping(
                    monitor='val_loss' if X_val is not None else 'loss',
                    patience=self.hyperparameters['patience'],
                    restore_best_weights=True
                ),
                ReduceLROnPlateau(
                    monitor='val_loss' if X_val is not None else 'loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-7
                )
            ]

            # Train the model
            validation_data_tuple = (X_val, y_val) if X_val is not None else None

            history = self.model.fit(
                X_train, y_train,
                batch_size=self.hyperparameters['batch_size'],
                epochs=self.hyperparameters['epochs'],
                validation_data=validation_data_tuple,
                callbacks=callbacks,
                verbose=1
            )

            self.is_trained = True
            self.training_history = history.history

            # Calculate performance metrics
            train_predictions = self.model.predict(X_train)
            train_mae = mean_absolute_error(y_train, train_predictions)
            train_rmse = np.sqrt(mean_squared_error(y_train, train_predictions))

            metrics = {
                'train_mae': train_mae,
                'train_rmse': train_rmse,
                'final_loss': history.history['loss'][-1]
            }

            if X_val is not None:
                val_predictions = self.model.predict(X_val)
                val_mae = mean_absolute_error(y_val, val_predictions)
                val_rmse = np.sqrt(mean_squared_error(y_val, val_predictions))
                metrics.update({
                    'val_mae': val_mae,
                    'val_rmse': val_rmse,
                    'final_val_loss': history.history['val_loss'][-1]
                })

            self.update_performance_metrics(metrics)

            # Update metadata
            self.metadata['training_samples'] = len(X_train)
            self.metadata['last_updated'] = pd.Timestamp.now()

            self.logger.info(f"LSTM training completed. Train MAE: {train_mae:.4f}, Train RMSE: {train_rmse:.4f}")

            return {
                'status': 'success',
                'metrics': metrics,
                'training_samples': len(X_train),
                'epochs_trained': len(history.history['loss'])
            }

        except Exception as e:
            self.logger.error(f"Error during LSTM training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def predict(self,
                     input_data: pd.DataFrame,
                     return_sequences: bool = False,
                     **kwargs) -> np.ndarray:
        """Make predictions using the trained LSTM model"""
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before making predictions")

            # Prepare input sequences
            X_pred = self._prepare_prediction_sequences(input_data)

            if X_pred is None or len(X_pred) == 0:
                raise ValueError("Insufficient data for prediction sequences")

            # Make predictions
            scaled_predictions = self.model.predict(X_pred)

            # Inverse transform predictions
            predictions = self.target_scaler.inverse_transform(scaled_predictions)

            if return_sequences:
                return predictions
            else:
                return predictions.flatten()

        except Exception as e:
            self.logger.error(f"Error during prediction: {e}")
            return np.array([])

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate model performance on test data"""
        try:
            # Prepare test sequences
            X_test, y_test = self._prepare_sequences(test_data, is_training=False)

            if X_test is None or len(X_test) == 0:
                return {'error': 'Insufficient test data'}

            # Make predictions
            predictions = self.model.predict(X_test)

            # Calculate metrics
            mae = mean_absolute_error(y_test, predictions)
            rmse = np.sqrt(mean_squared_error(y_test, predictions))
            mape = np.mean(np.abs((y_test - predictions) / y_test)) * 100

            # Directional accuracy (for trend prediction)
            actual_direction = np.sign(np.diff(y_test.flatten()))
            pred_direction = np.sign(np.diff(predictions.flatten()))
            directional_accuracy = np.mean(actual_direction == pred_direction)

            metrics = {
                'test_mae': mae,
                'test_rmse': rmse,
                'test_mape': mape,
                'directional_accuracy': directional_accuracy
            }

            self.logger.info(f"LSTM evaluation - MAE: {mae:.4f}, RMSE: {rmse:.4f}, Directional Accuracy: {directional_accuracy:.3f}")

            return metrics

        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}")
            return {'error': str(e)}

    def _build_model(self, sequence_length: int, n_features: int) -> tf.keras.Model:
        """Build the LSTM neural network architecture"""
        model = Sequential()

        # First LSTM layer
        model.add(LSTM(
            units=self.hyperparameters['lstm_units'][0],
            return_sequences=True,
            input_shape=(sequence_length, n_features)
        ))
        model.add(Dropout(self.hyperparameters['dropout_rate']))
        model.add(BatchNormalization())

        # Second LSTM layer
        model.add(LSTM(
            units=self.hyperparameters['lstm_units'][1],
            return_sequences=True
        ))
        model.add(Dropout(self.hyperparameters['dropout_rate']))
        model.add(BatchNormalization())

        # Third LSTM layer
        model.add(LSTM(
            units=self.hyperparameters['lstm_units'][2],
            return_sequences=False
        ))
        model.add(Dropout(self.hyperparameters['dropout_rate']))

        # Output layer
        model.add(Dense(self.prediction_horizon))

        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=self.hyperparameters['learning_rate']),
            loss='mse',
            metrics=['mae']
        )

        return model

    def _prepare_sequences(self, data: pd.DataFrame, is_training: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM input"""
        try:
            # Define features and target
            feature_cols = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
            target_col = 'close_price'

            # Filter available columns
            available_features = [col for col in feature_cols if col in data.columns]
            if not available_features:
                self.logger.error("No required features found in data")
                return None, None

            # Prepare feature data
            feature_data = data[available_features].values
            target_data = data[target_col].values.reshape(-1, 1)

            if is_training:
                # Fit scalers on training data
                scaled_features = self.scaler.fit_transform(feature_data)
                scaled_target = self.target_scaler.fit_transform(target_data)
                self.feature_columns = available_features
                self.target_columns = [target_col]
            else:
                # Use fitted scalers
                scaled_features = self.scaler.transform(feature_data)
                scaled_target = self.target_scaler.transform(target_data)

            # Create sequences
            X, y = [], []
            for i in range(self.sequence_length, len(scaled_features) - self.prediction_horizon + 1):
                X.append(scaled_features[i-self.sequence_length:i])
                y.append(scaled_target[i:i+self.prediction_horizon])

            return np.array(X), np.array(y)

        except Exception as e:
            self.logger.error(f"Error preparing sequences: {e}")
            return None, None

    def _prepare_prediction_sequences(self, data: pd.DataFrame) -> np.ndarray:
        """Prepare sequences for prediction only"""
        try:
            # Use only the features from training
            feature_data = data[self.feature_columns].values

            # Scale features
            scaled_features = self.scaler.transform(feature_data)

            # Create sequences (only need the last sequence for prediction)
            if len(scaled_features) < self.sequence_length:
                self.logger.warning(f"Insufficient data for prediction. Need {self.sequence_length}, got {len(scaled_features)}")
                return None

            # Take the last sequence
            X = scaled_features[-self.sequence_length:].reshape(1, self.sequence_length, -1)

            return X

        except Exception as e:
            self.logger.error(f"Error preparing prediction sequences: {e}")
            return None

    def calculate_prediction_confidence(self, predictions: np.ndarray) -> np.ndarray:
        """Calculate confidence scores based on prediction consistency"""
        try:
            # For LSTM, confidence can be based on prediction variance
            # This is a simplified approach - in practice, you might use ensemble methods
            if len(predictions) < 2:
                return np.array([0.5])

            # Calculate rolling standard deviation as inverse confidence measure
            variance = np.var(predictions[-min(10, len(predictions)):])
            confidence = 1.0 / (1.0 + variance)  # Higher variance = lower confidence

            return np.array([confidence])

        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return np.array([0.5])

    async def predict_with_confidence(self, input_data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Make predictions with confidence scores"""
        predictions = await self.predict(input_data)
        confidence = self.calculate_prediction_confidence(predictions)
        return predictions, confidence

    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance (simplified for LSTM)"""
        if not self.feature_columns:
            return {}

        # For LSTM, feature importance is not directly available
        # Return equal importance for all features
        importance = 1.0 / len(self.feature_columns)
        return {col: importance for col in self.feature_columns}