"""
LSTM Neural Network Predictor for Time Series Forecasting
PyTorch implementation for better CPU compatibility
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from typing import Dict, List, Tuple, Any, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

from .base_model import BaseMLModel

class PyTorchLSTM(nn.Module):
    """PyTorch LSTM implementation"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int, dropout: float = 0.2):
        super().__init__()
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.output_size = output_size
        
        # Build LSTM layers
        self.lstm_layers = nn.ModuleList()
        prev_size = input_size
        
        for hidden_size in hidden_sizes:
            self.lstm_layers.append(nn.LSTM(prev_size, hidden_size, batch_first=True, dropout=dropout))
            prev_size = hidden_size
        
        # Output layer
        self.output_layer = nn.Linear(prev_size, output_size)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # x shape: (batch, sequence, features)
        for lstm in self.lstm_layers:
            x, _ = lstm(x)
            x = self.dropout(x)
        
        # Take the last output
        x = x[:, -1, :]
        x = self.output_layer(x)
        return x

class LSTMPredictor(BaseMLModel):
    """LSTM Neural Network for price and trend prediction using PyTorch"""

    def __init__(self, model_id: str, sequence_length: int = 60, prediction_horizon: int = 1):
        super().__init__(model_id, "LSTM", "1.0")

        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.target_scaler = MinMaxScaler(feature_range=(0, 1))
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.use_pytorch = True  # Always use PyTorch now
        
        # Fallback models
        self.fallback_models = None

        # Model hyperparameters
        self.hyperparameters = {
            'lstm_units': [50, 50, 25],
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 100,
            'patience': 15
        }

        self.metadata['hyperparameters'] = self.hyperparameters

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train the LSTM model"""
        try:
            self.logger.info(f"Starting LSTM training with {len(training_data)} samples")

            # Update hyperparameters with any provided kwargs
            self.hyperparameters.update(kwargs)

            # Prepare training data
            X_train, y_train = self._prepare_sequences(training_data)
            
            if X_train is None or len(X_train) == 0:
                raise ValueError("Insufficient training data")

            # Prepare validation data if provided
            X_val, y_val = None, None
            if validation_data is not None:
                X_val, y_val = self._prepare_sequences(validation_data)

            # Try PyTorch training first
            try:
                result = await self._train_pytorch(X_train, y_train, X_val, y_val)
                if result['status'] == 'success':
                    self.is_trained = True
                    return result
            except Exception as e:
                self.logger.warning(f"PyTorch training failed: {e}")
            
            # Fallback to scikit-learn
            self.logger.info("Using scikit-learn fallback models")
            result = await self._train_sklearn_fallback(X_train, y_train, X_val, y_val)
            self.is_trained = True
            return result

        except Exception as e:
            self.logger.error(f"Error during LSTM training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def _train_pytorch(self, X_train, y_train, X_val=None, y_val=None):
        """Train using PyTorch"""
        # Convert to tensors
        X_tensor = torch.FloatTensor(X_train).to(self.device)
        y_tensor = torch.FloatTensor(y_train).to(self.device)
        
        # Create model
        input_size = X_train.shape[2]
        self.model = PyTorchLSTM(
            input_size, 
            self.hyperparameters['lstm_units'], 
            self.prediction_horizon,
            self.hyperparameters['dropout_rate']
        ).to(self.device)
        
        # Training setup
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.hyperparameters['learning_rate'])
        
        # Create data loader
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.hyperparameters['batch_size'], shuffle=True)
        
        # Training loop
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.hyperparameters['epochs']):
            total_loss = 0
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                outputs = self.model(batch_X).squeeze()
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
            
            avg_loss = total_loss / len(dataloader)
            
            # Validation
            if X_val is not None:
                val_loss = self._validate_pytorch(X_val, y_val, criterion)
                if val_loss < best_loss:
                    best_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= self.hyperparameters['patience']:
                        self.logger.info(f"Early stopping at epoch {epoch}")
                        break
            
            if epoch % 10 == 0:
                self.logger.info(f"Epoch {epoch}, Loss: {avg_loss:.6f}")
        
        return {"status": "success", "model_type": "pytorch_lstm", "final_loss": avg_loss}

    def _validate_pytorch(self, X_val, y_val, criterion):
        """Validate PyTorch model"""
        self.model.eval()
        with torch.no_grad():
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.FloatTensor(y_val).to(self.device)
            outputs = self.model(X_val_tensor).squeeze()
            val_loss = criterion(outputs, y_val_tensor).item()
        self.model.train()
        return val_loss

    async def _train_sklearn_fallback(self, X_train, y_train, X_val=None, y_val=None):
        """Train using scikit-learn as fallback"""
        # Flatten sequences for sklearn
        X_flat = X_train.reshape(X_train.shape[0], -1)
        
        # Create ensemble of models
        self.fallback_models = {
            'rf': RandomForestRegressor(n_estimators=100, random_state=42),
            'gb': GradientBoostingRegressor(n_estimators=100, random_state=42)
        }
        
        # Train all models
        for name, model in self.fallback_models.items():
            model.fit(X_flat, y_train)
            self.logger.info(f"Trained {name} fallback model")
        
        # Calculate training metrics
        predictions = self._predict_sklearn_ensemble(X_flat)
        mae = mean_absolute_error(y_train, predictions)
        mse = mean_squared_error(y_train, predictions)
        
        return {
            "status": "success", 
            "model_type": "sklearn_ensemble",
            "mae": mae,
            "mse": mse
        }

    def _predict_sklearn_ensemble(self, X_flat):
        """Make ensemble predictions with sklearn models"""
        predictions = []
        for model in self.fallback_models.values():
            pred = model.predict(X_flat)
            predictions.append(pred)
        return np.mean(predictions, axis=0)

    async def predict(self,
                     data: pd.DataFrame,
                     **kwargs) -> Dict[str, Any]:
        """Make predictions using the trained model"""
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before making predictions")

            # Prepare prediction data
            X_pred, _ = self._prepare_sequences(data, for_prediction=True)
            
            if X_pred is None or len(X_pred) == 0:
                raise ValueError("Insufficient data for prediction")

            # Make predictions
            if self.model is not None and isinstance(self.model, nn.Module):
                predictions = self._predict_pytorch(X_pred)
            elif self.fallback_models is not None:
                X_flat = X_pred.reshape(X_pred.shape[0], -1)
                predictions = self._predict_sklearn_ensemble(X_flat)
            else:
                raise ValueError("No trained model available")

            # Inverse transform predictions
            predictions_scaled = predictions.reshape(-1, 1)
            predictions_original = self.target_scaler.inverse_transform(predictions_scaled).flatten()

            return {
                'predictions': predictions_original.tolist(),
                'confidence': self._calculate_confidence(predictions),
                'model_type': 'pytorch' if self.model else 'sklearn'
            }

        except Exception as e:
            self.logger.error(f"Error during prediction: {e}")
            return {'error': str(e)}

    def _predict_pytorch(self, X_pred):
        """Make predictions using PyTorch model"""
        self.model.eval()
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X_pred).to(self.device)
            predictions = self.model(X_tensor).cpu().numpy().squeeze()
        return predictions

    def _prepare_sequences(self, data: pd.DataFrame, for_prediction: bool = False):
        """Prepare sequences for LSTM training/prediction"""
        try:
            # Select numeric columns
            numeric_data = data.select_dtypes(include=[np.number])
            
            if len(numeric_data.columns) == 0:
                raise ValueError("No numeric columns found in data")

            # Scale the data
            if not for_prediction:
                scaled_data = self.scaler.fit_transform(numeric_data)
                # Fit target scaler on the first column (assumed to be target)
                self.target_scaler.fit(numeric_data.iloc[:, 0].values.reshape(-1, 1))
            else:
                scaled_data = self.scaler.transform(numeric_data)

            # Create sequences
            X, y = [], []
            for i in range(self.sequence_length, len(scaled_data)):
                X.append(scaled_data[i-self.sequence_length:i])
                if not for_prediction:
                    y.append(scaled_data[i, 0])  # Predict first column

            if len(X) == 0:
                return None, None

            X = np.array(X)
            y = np.array(y) if not for_prediction else None

            return X, y

        except Exception as e:
            self.logger.error(f"Error preparing sequences: {e}")
            return None, None

    def _calculate_confidence(self, predictions):
        """Calculate prediction confidence"""
        if len(predictions) < 2:
            return 0.5

        # Simple confidence based on prediction variance
        variance = np.var(predictions)
        confidence = max(0.1, min(0.9, 1.0 / (1.0 + variance)))
        return confidence

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate model performance on test data"""
        try:
            if not self.is_trained:
                return {'error': 'Model not trained yet'}

            # Prepare test data
            X_test, y_test = self._prepare_sequences(test_data)

            if X_test is None or len(X_test) == 0:
                return {'error': 'Insufficient test data'}

            # Make predictions
            if self.model is not None and isinstance(self.model, nn.Module):
                predictions = self._predict_pytorch(X_test)
            elif self.fallback_models is not None:
                X_flat = X_test.reshape(X_test.shape[0], -1)
                predictions = self._predict_sklearn_ensemble(X_flat)
            else:
                return {'error': 'No trained model available'}

            # Calculate metrics
            mae = mean_absolute_error(y_test, predictions)
            mse = mean_squared_error(y_test, predictions)
            rmse = np.sqrt(mse)

            # Calculate R² score
            try:
                r2 = r2_score(y_test, predictions)
            except:
                r2 = 0.0

            return {
                'mae': float(mae),
                'mse': float(mse),
                'rmse': float(rmse),
                'r2_score': float(r2),
                'model_type': 'pytorch' if self.model else 'sklearn'
            }

        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}")
            return {'error': str(e)}
