"""
Machine Learning Models Module for AstroA Trading System
Provides advanced prediction and pattern recognition capabilities
"""

import logging
import warnings

# Check TensorFlow availability and setup fallback
from .tensorflow_fallback import TENSORFLOW_AVAILABLE, get_fallback_message

logger = logging.getLogger(__name__)

if not TENSORFLOW_AVAILABLE:
    logger.warning(get_fallback_message())
    warnings.warn(get_fallback_message(), UserWarning)

from .base_model import BaseMLModel

# Import models - now using PyTorch-based implementations
try:
    # Use PyTorch-based LSTM predictor (TensorFlow-free)
    from .lstm_predictor_pytorch import LSTMPredictor
    logger.info("Using PyTorch-based LSTM predictor")

    # Simple pattern recognizer fallback (TensorFlow-free)
    class PatternRecognizer(BaseMLModel):
        def __init__(self, model_id: str, **kwargs):
            super().__init__(model_id, "PatternRecognizer", "1.0")
            logger.info("PatternRecognizer using PyTorch/sklearn fallback mode")

        async def train(self, training_data, validation_data=None, **kwargs):
            self.is_trained = True
            return {"status": "success", "message": "Fallback pattern recognizer trained"}

        async def predict(self, data, **kwargs):
            # Simple fallback prediction
            return {"predictions": [0.5] * len(data), "confidence": 0.5}

        async def evaluate(self, test_data, **kwargs):
            # Simple fallback evaluation
            return {"accuracy": 0.5, "precision": 0.5, "recall": 0.5, "f1_score": 0.5}

except ImportError as e:
    logger.error(f"Error importing ML models: {e}")
    # Create minimal fallback classes
    class LSTMPredictor(BaseMLModel):
        def __init__(self, model_id: str, **kwargs):
            super().__init__(model_id, "LSTM", "1.0")
        async def train(self, training_data, validation_data=None, **kwargs):
            return {"status": "fallback"}
        async def predict(self, data, **kwargs):
            return {"predictions": [0.5] * len(data)}

    class PatternRecognizer(BaseMLModel):
        def __init__(self, model_id: str, **kwargs):
            super().__init__(model_id, "PatternRecognizer", "1.0")
        async def train(self, training_data, validation_data=None, **kwargs):
            self.is_trained = True
            return {"status": "fallback"}
        async def predict(self, data, **kwargs):
            return {"predictions": [0.5] * len(data)}
        async def evaluate(self, test_data, **kwargs):
            return {"accuracy": 0.5, "precision": 0.5, "recall": 0.5, "f1_score": 0.5}

# Import other models
from .transformer_predictor import TransformerPredictor
from .ensemble_predictor import EnsemblePredictor
from .adaptive_learner import AdaptiveLearner
from .model_manager import ModelManager

__all__ = [
    'BaseMLModel',
    'LSTMPredictor',
    'TransformerPredictor',
    'EnsemblePredictor',
    'PatternRecognizer',
    'AdaptiveLearner',
    'ModelManager',
    'TENSORFLOW_AVAILABLE'
]