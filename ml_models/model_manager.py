"""
Model Manager for ML Model Lifecycle Management
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import asyncio
import logging
import json
from pathlib import Path
import pickle
import joblib

from .base_model import BaseMLModel
# Dynamic imports to avoid TensorFlow issues
from .ensemble_predictor import EnsemblePredictor
from .adaptive_learner import AdaptiveLearner

class ModelManager:
    """Centralized manager for all ML models in the trading system"""

    def __init__(self, models_directory: str = "models"):
        self.models_directory = Path(models_directory)
        self.models_directory.mkdir(exist_ok=True)

        # Active models registry
        self.active_models: Dict[str, BaseMLModel] = {}
        self.model_metadata: Dict[str, Dict] = {}

        # Performance tracking
        self.model_performances: Dict[str, List[Dict]] = {}

        # Model lifecycle settings
        self.lifecycle_settings = {
            'auto_retrain_threshold': 0.15,  # Retrain if performance drops 15%
            'model_expiry_days': 30,  # Models expire after 30 days
            'min_training_samples': 1000,  # Minimum samples for training
            'performance_window': 100,  # Window for performance evaluation
            'backup_frequency_hours': 24  # Backup models every 24 hours
        }

        # Setup logging
        self.logger = logging.getLogger("ModelManager")

        # Model factory - dynamic loading to avoid TensorFlow issues
        self.model_factory = {
            'ensemble': EnsemblePredictor,
            'adaptive_learner': AdaptiveLearner
        }

        # Try to add LSTM and Pattern Recognizer with fallback
        try:
            from .lstm_predictor_pytorch import LSTMPredictor
            self.model_factory['lstm'] = LSTMPredictor
        except ImportError:
            self.logger.warning("LSTM predictor not available")

        try:
            # Create a simple pattern recognizer class
            class SimplePatternRecognizer(BaseMLModel):
                def __init__(self, model_id: str, **kwargs):
                    super().__init__(model_id, "PatternRecognizer", "1.0")
                async def train(self, training_data, validation_data=None, **kwargs):
                    return {"status": "success", "message": "Simple pattern recognizer"}
                async def predict(self, data, **kwargs):
                    return {"predictions": [0.5] * len(data), "confidence": 0.5}

            self.model_factory['pattern_recognizer'] = SimplePatternRecognizer
        except Exception as e:
            self.logger.warning(f"Pattern recognizer not available: {e}")

    async def create_model(self,
                          model_type: str,
                          model_id: str,
                          **kwargs) -> BaseMLModel:
        """Create a new model instance"""
        try:
            if model_type not in self.model_factory:
                raise ValueError(f"Unknown model type: {model_type}")

            # Create model instance
            model_class = self.model_factory[model_type]
            model = model_class(model_id, **kwargs)

            # Register the model
            self.active_models[model_id] = model
            self.model_metadata[model_id] = {
                'model_type': model_type,
                'created_at': datetime.now(),
                'last_updated': None,
                'training_status': 'untrained',
                'performance_history': [],
                'kwargs': kwargs
            }

            self.logger.info(f"Created {model_type} model: {model_id}")
            return model

        except Exception as e:
            self.logger.error(f"Error creating model {model_id}: {e}")
            raise

    async def train_model(self,
                         model_id: str,
                         training_data: pd.DataFrame,
                         validation_data: pd.DataFrame = None,
                         **kwargs) -> Dict[str, Any]:
        """Train a specific model"""
        try:
            if model_id not in self.active_models:
                raise ValueError(f"Model {model_id} not found")

            model = self.active_models[model_id]

            self.logger.info(f"Training model {model_id} with {len(training_data)} samples")

            # Validate training data
            if len(training_data) < self.lifecycle_settings['min_training_samples']:
                self.logger.warning(f"Training data below minimum threshold: {len(training_data)}")

            # Train the model
            result = await model.train(training_data, validation_data, **kwargs)

            # Update metadata
            if result['status'] == 'success':
                self.model_metadata[model_id].update({
                    'last_updated': datetime.now(),
                    'training_status': 'trained',
                    'training_samples': len(training_data),
                    'last_training_metrics': result.get('metrics', {})
                })

                # Record performance
                self._record_model_performance(model_id, result.get('metrics', {}))

                self.logger.info(f"Successfully trained model {model_id}")
            else:
                self.model_metadata[model_id]['training_status'] = 'failed'
                self.logger.error(f"Training failed for model {model_id}: {result.get('message', 'Unknown error')}")

            return result

        except Exception as e:
            self.logger.error(f"Error training model {model_id}: {e}")
            return {'status': 'error', 'message': str(e)}

    async def predict_with_model(self,
                                model_id: str,
                                input_data: pd.DataFrame,
                                **kwargs) -> np.ndarray:
        """Make predictions using a specific model"""
        try:
            if model_id not in self.active_models:
                raise ValueError(f"Model {model_id} not found")

            model = self.active_models[model_id]

            if not model.is_trained:
                raise ValueError(f"Model {model_id} is not trained")

            # Make prediction
            prediction = await model.predict(input_data, **kwargs)

            # Log prediction for monitoring
            self._log_prediction(model_id, input_data, prediction)

            return prediction

        except Exception as e:
            self.logger.error(f"Error making prediction with model {model_id}: {e}")
            return np.array([])

    async def ensemble_predict(self,
                              model_ids: List[str],
                              input_data: pd.DataFrame,
                              weights: Optional[List[float]] = None) -> np.ndarray:
        """Make ensemble predictions from multiple models"""
        try:
            predictions = []
            valid_weights = []

            for i, model_id in enumerate(model_ids):
                try:
                    pred = await self.predict_with_model(model_id, input_data)
                    if len(pred) > 0:
                        predictions.append(pred)
                        valid_weights.append(weights[i] if weights else 1.0)
                except Exception as e:
                    self.logger.warning(f"Failed to get prediction from {model_id}: {e}")
                    continue

            if not predictions:
                return np.array([])

            # Combine predictions
            if len(predictions) == 1:
                return predictions[0]

            # Weighted average
            prediction_array = np.array(predictions)
            weight_array = np.array(valid_weights)
            weight_array = weight_array / weight_array.sum()

            ensemble_prediction = np.average(prediction_array, axis=0, weights=weight_array)

            self.logger.info(f"Ensemble prediction from {len(predictions)} models")
            return ensemble_prediction

        except Exception as e:
            self.logger.error(f"Error making ensemble prediction: {e}")
            return np.array([])

    async def evaluate_model(self,
                           model_id: str,
                           test_data: pd.DataFrame,
                           **kwargs) -> Dict[str, float]:
        """Evaluate a specific model"""
        try:
            if model_id not in self.active_models:
                raise ValueError(f"Model {model_id} not found")

            model = self.active_models[model_id]
            metrics = await model.evaluate(test_data, **kwargs)

            # Record evaluation results
            self._record_model_performance(model_id, metrics, evaluation=True)

            return metrics

        except Exception as e:
            self.logger.error(f"Error evaluating model {model_id}: {e}")
            return {'error': str(e)}

    async def save_model(self, model_id: str, filepath: Optional[str] = None) -> bool:
        """Save a model to disk"""
        try:
            if model_id not in self.active_models:
                raise ValueError(f"Model {model_id} not found")

            model = self.active_models[model_id]

            if filepath is None:
                filepath = self.models_directory / f"{model_id}.joblib"

            success = model.save_model(str(filepath))

            if success:
                # Save metadata
                metadata_path = self.models_directory / f"{model_id}_metadata.json"
                with open(metadata_path, 'w') as f:
                    metadata = self.model_metadata[model_id].copy()
                    # Convert datetime objects to strings
                    for key, value in metadata.items():
                        if isinstance(value, datetime):
                            metadata[key] = value.isoformat()
                    json.dump(metadata, f, indent=2)

                self.logger.info(f"Saved model {model_id} to {filepath}")

            return success

        except Exception as e:
            self.logger.error(f"Error saving model {model_id}: {e}")
            return False

    async def load_model(self, model_id: str, filepath: Optional[str] = None) -> bool:
        """Load a model from disk"""
        try:
            if filepath is None:
                filepath = self.models_directory / f"{model_id}.joblib"

            if not Path(filepath).exists():
                raise FileNotFoundError(f"Model file not found: {filepath}")

            # Load metadata first to determine model type
            metadata_path = self.models_directory / f"{model_id}_metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)

                model_type = metadata.get('model_type')
                if model_type not in self.model_factory:
                    raise ValueError(f"Unknown model type in metadata: {model_type}")

                # Create model instance
                model_class = self.model_factory[model_type]
                kwargs = metadata.get('kwargs', {})
                model = model_class(model_id, **kwargs)

                # Load the trained model
                success = model.load_model(str(filepath))

                if success:
                    self.active_models[model_id] = model
                    self.model_metadata[model_id] = metadata

                    # Convert string dates back to datetime
                    for key, value in self.model_metadata[model_id].items():
                        if key in ['created_at', 'last_updated'] and isinstance(value, str):
                            self.model_metadata[model_id][key] = datetime.fromisoformat(value)

                    self.logger.info(f"Loaded model {model_id} from {filepath}")

                return success

            else:
                self.logger.error(f"Metadata file not found: {metadata_path}")
                return False

        except Exception as e:
            self.logger.error(f"Error loading model {model_id}: {e}")
            return False

    async def retrain_model(self,
                           model_id: str,
                           new_data: pd.DataFrame,
                           incremental: bool = True,
                           **kwargs) -> Dict[str, Any]:
        """Retrain a model with new data"""
        try:
            if model_id not in self.active_models:
                raise ValueError(f"Model {model_id} not found")

            model = self.active_models[model_id]

            self.logger.info(f"Retraining model {model_id} with {len(new_data)} new samples")

            # Retrain the model
            result = await model.retrain(new_data, incremental=incremental, **kwargs)

            # Update metadata
            if result['status'] == 'success':
                self.model_metadata[model_id].update({
                    'last_updated': datetime.now(),
                    'retrain_count': self.model_metadata[model_id].get('retrain_count', 0) + 1
                })

                # Record performance
                self._record_model_performance(model_id, result.get('metrics', {}))

                self.logger.info(f"Successfully retrained model {model_id}")

            return result

        except Exception as e:
            self.logger.error(f"Error retraining model {model_id}: {e}")
            return {'status': 'error', 'message': str(e)}

    async def auto_manage_models(self):
        """Automatically manage model lifecycle"""
        try:
            self.logger.info("Starting automatic model management")

            for model_id, model in self.active_models.items():
                metadata = self.model_metadata[model_id]

                # Check if model needs retraining
                if self._should_retrain_model(model_id):
                    self.logger.info(f"Model {model_id} scheduled for retraining")
                    # This would trigger retraining with new data
                    # Implementation depends on data availability

                # Check if model has expired
                if self._is_model_expired(model_id):
                    self.logger.warning(f"Model {model_id} has expired")
                    # Could automatically retrain or flag for attention

                # Backup model if needed
                if self._should_backup_model(model_id):
                    await self.save_model(model_id)

        except Exception as e:
            self.logger.error(f"Error in automatic model management: {e}")

    def get_model_status(self, model_id: str) -> Dict[str, Any]:
        """Get comprehensive status of a model"""
        try:
            if model_id not in self.active_models:
                return {'error': 'Model not found'}

            model = self.active_models[model_id]
            metadata = self.model_metadata[model_id]

            # Get recent performance
            recent_performance = self._get_recent_performance(model_id)

            status = {
                'model_id': model_id,
                'model_type': metadata['model_type'],
                'is_trained': model.is_trained,
                'training_status': metadata.get('training_status', 'unknown'),
                'created_at': metadata['created_at'],
                'last_updated': metadata.get('last_updated'),
                'training_samples': metadata.get('training_samples', 0),
                'retrain_count': metadata.get('retrain_count', 0),
                'recent_performance': recent_performance,
                'model_info': model.get_model_info()
            }

            return status

        except Exception as e:
            self.logger.error(f"Error getting model status for {model_id}: {e}")
            return {'error': str(e)}

    def list_models(self) -> List[Dict[str, Any]]:
        """List all active models with basic information"""
        models_list = []

        for model_id, model in self.active_models.items():
            metadata = self.model_metadata[model_id]

            model_info = {
                'model_id': model_id,
                'model_type': metadata['model_type'],
                'is_trained': model.is_trained,
                'created_at': metadata['created_at'],
                'last_updated': metadata.get('last_updated'),
                'training_status': metadata.get('training_status', 'unknown')
            }

            models_list.append(model_info)

        return models_list

    def _record_model_performance(self, model_id: str, metrics: Dict[str, float], evaluation: bool = False):
        """Record model performance metrics"""
        try:
            if model_id not in self.model_performances:
                self.model_performances[model_id] = []

            performance_record = {
                'timestamp': datetime.now(),
                'metrics': metrics,
                'type': 'evaluation' if evaluation else 'training'
            }

            self.model_performances[model_id].append(performance_record)

            # Keep only recent performance records
            max_records = self.lifecycle_settings['performance_window']
            if len(self.model_performances[model_id]) > max_records:
                self.model_performances[model_id] = self.model_performances[model_id][-max_records:]

        except Exception as e:
            self.logger.error(f"Error recording performance for {model_id}: {e}")

    def _log_prediction(self, model_id: str, input_data: pd.DataFrame, prediction: np.ndarray):
        """Log prediction for monitoring"""
        # This could be extended to store predictions for later analysis
        pass

    def _should_retrain_model(self, model_id: str) -> bool:
        """Determine if a model should be retrained"""
        try:
            if model_id not in self.model_performances:
                return False

            performances = self.model_performances[model_id]
            if len(performances) < 2:
                return False

            # Get recent vs older performance
            recent = performances[-5:]  # Last 5 records
            older = performances[-10:-5]  # Previous 5 records

            if not older:
                return False

            # Calculate average performance (using MAE as example)
            recent_mae = np.mean([p['metrics'].get('mae', p['metrics'].get('train_mae', 0)) for p in recent])
            older_mae = np.mean([p['metrics'].get('mae', p['metrics'].get('train_mae', 0)) for p in older])

            if older_mae > 0:
                performance_degradation = (recent_mae - older_mae) / older_mae
                return performance_degradation > self.lifecycle_settings['auto_retrain_threshold']

            return False

        except Exception as e:
            self.logger.error(f"Error checking retrain condition for {model_id}: {e}")
            return False

    def _is_model_expired(self, model_id: str) -> bool:
        """Check if a model has expired"""
        try:
            metadata = self.model_metadata[model_id]
            last_updated = metadata.get('last_updated', metadata['created_at'])

            if isinstance(last_updated, str):
                last_updated = datetime.fromisoformat(last_updated)

            age_days = (datetime.now() - last_updated).days
            return age_days > self.lifecycle_settings['model_expiry_days']

        except Exception as e:
            self.logger.error(f"Error checking expiry for {model_id}: {e}")
            return False

    def _should_backup_model(self, model_id: str) -> bool:
        """Check if a model should be backed up"""
        try:
            model_file = self.models_directory / f"{model_id}.joblib"
            if not model_file.exists():
                return True

            # Check file age
            file_age_hours = (datetime.now() - datetime.fromtimestamp(model_file.stat().st_mtime)).total_seconds() / 3600
            return file_age_hours > self.lifecycle_settings['backup_frequency_hours']

        except Exception as e:
            self.logger.error(f"Error checking backup condition for {model_id}: {e}")
            return False

    def _get_recent_performance(self, model_id: str) -> Dict[str, float]:
        """Get recent performance metrics for a model"""
        try:
            if model_id not in self.model_performances:
                return {}

            recent_performances = self.model_performances[model_id][-5:]  # Last 5 records
            if not recent_performances:
                return {}

            # Average recent metrics
            all_metrics = {}
            for perf in recent_performances:
                for metric, value in perf['metrics'].items():
                    if metric not in all_metrics:
                        all_metrics[metric] = []
                    all_metrics[metric].append(value)

            return {metric: np.mean(values) for metric, values in all_metrics.items()}

        except Exception as e:
            self.logger.error(f"Error getting recent performance for {model_id}: {e}")
            return {}