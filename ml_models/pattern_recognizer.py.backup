"""
Neural Network Pattern Recognizer for Trading Patterns
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, precision_recall_fscore_support
from typing import Dict, List, Tuple, Any, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

from .base_model import BaseMLModel

class PatternRecognizer(BaseMLModel):
    """Convolutional Neural Network for recognizing trading patterns"""

    def __init__(self, model_id: str, pattern_length: int = 20):
        super().__init__(model_id, "PatternRecognizer", "1.0")

        self.pattern_length = pattern_length
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()

        # Pattern types to recognize
        self.pattern_types = [
            'bullish_flag',
            'bearish_flag',
            'head_shoulders',
            'inverse_head_shoulders',
            'double_top',
            'double_bottom',
            'triangle_ascending',
            'triangle_descending',
            'wedge_rising',
            'wedge_falling',
            'channel_up',
            'channel_down',
            'consolidation',
            'breakout_up',
            'breakout_down',
            'no_pattern'
        ]

        # Model hyperparameters
        self.hyperparameters = {
            'conv_filters': [32, 64, 128],
            'conv_kernel_size': 3,
            'pool_size': 2,
            'lstm_units': 50,
            'dense_units': 100,
            'dropout_rate': 0.3,
            'learning_rate': 0.001,
            'batch_size': 64,
            'epochs': 50
        }

        self.metadata['hyperparameters'] = self.hyperparameters

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train the pattern recognition model"""
        try:
            self.logger.info(f"Starting pattern recognition training with {len(training_data)} samples")

            # Update hyperparameters
            self.hyperparameters.update(kwargs)

            # Prepare training data
            X_train, y_train = self._prepare_pattern_data(training_data, is_training=True)

            if X_train is None or len(X_train) == 0:
                raise ValueError("Insufficient training data for pattern recognition")

            # Prepare validation data
            X_val, y_val = None, None
            if validation_data is not None:
                X_val, y_val = self._prepare_pattern_data(validation_data, is_training=False)

            # Build and compile model
            self.model = self._build_pattern_model(X_train.shape[1], X_train.shape[2])

            # Train the model
            validation_data_tuple = (X_val, y_val) if X_val is not None else None

            history = self.model.fit(
                X_train, y_train,
                batch_size=self.hyperparameters['batch_size'],
                epochs=self.hyperparameters['epochs'],
                validation_data=validation_data_tuple,
                verbose=1
            )

            self.is_trained = True
            self.training_history = history.history

            # Calculate performance metrics
            train_predictions = self.model.predict(X_train)
            train_pred_classes = np.argmax(train_predictions, axis=1)
            train_true_classes = np.argmax(y_train, axis=1)

            train_accuracy = accuracy_score(train_true_classes, train_pred_classes)
            precision, recall, f1, _ = precision_recall_fscore_support(
                train_true_classes, train_pred_classes, average='weighted'
            )

            metrics = {
                'train_accuracy': train_accuracy,
                'train_precision': precision,
                'train_recall': recall,
                'train_f1': f1
            }

            if X_val is not None:
                val_predictions = self.model.predict(X_val)
                val_pred_classes = np.argmax(val_predictions, axis=1)
                val_true_classes = np.argmax(y_val, axis=1)

                val_accuracy = accuracy_score(val_true_classes, val_pred_classes)
                val_precision, val_recall, val_f1, _ = precision_recall_fscore_support(
                    val_true_classes, val_pred_classes, average='weighted'
                )

                metrics.update({
                    'val_accuracy': val_accuracy,
                    'val_precision': val_precision,
                    'val_recall': val_recall,
                    'val_f1': val_f1
                })

            self.update_performance_metrics(metrics)

            # Update metadata
            self.metadata['training_samples'] = len(X_train)
            self.metadata['last_updated'] = pd.Timestamp.now()

            self.logger.info(f"Pattern recognition training completed. Train Accuracy: {train_accuracy:.4f}")

            return {
                'status': 'success',
                'metrics': metrics,
                'training_samples': len(X_train),
                'pattern_types': len(self.pattern_types)
            }

        except Exception as e:
            self.logger.error(f"Error during pattern recognition training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def predict(self,
                     input_data: pd.DataFrame,
                     return_probabilities: bool = False,
                     **kwargs) -> np.ndarray:
        """Recognize patterns in input data"""
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before making predictions")

            # Prepare input data
            X_pred = self._prepare_pattern_sequences(input_data)

            if X_pred is None or len(X_pred) == 0:
                raise ValueError("Insufficient data for pattern recognition")

            # Make predictions
            predictions = self.model.predict(X_pred)

            if return_probabilities:
                return predictions
            else:
                # Return class predictions
                predicted_classes = np.argmax(predictions, axis=1)
                return self.label_encoder.inverse_transform(predicted_classes)

        except Exception as e:
            self.logger.error(f"Error during pattern recognition: {e}")
            return np.array([])

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate pattern recognition performance"""
        try:
            # Prepare test data
            X_test, y_test = self._prepare_pattern_data(test_data, is_training=False)

            if X_test is None or len(X_test) == 0:
                return {'error': 'Insufficient test data'}

            # Make predictions
            predictions = self.model.predict(X_test)
            pred_classes = np.argmax(predictions, axis=1)
            true_classes = np.argmax(y_test, axis=1)

            # Calculate metrics
            accuracy = accuracy_score(true_classes, pred_classes)
            precision, recall, f1, _ = precision_recall_fscore_support(
                true_classes, pred_classes, average='weighted'
            )

            # Per-class metrics
            class_report = classification_report(
                true_classes, pred_classes,
                target_names=self.pattern_types[:len(np.unique(true_classes))],
                output_dict=True
            )

            metrics = {
                'test_accuracy': accuracy,
                'test_precision': precision,
                'test_recall': recall,
                'test_f1': f1,
                'class_metrics': class_report
            }

            self.logger.info(f"Pattern recognition evaluation - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")

            return metrics

        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}")
            return {'error': str(e)}

    def _build_pattern_model(self, sequence_length: int, n_features: int) -> tf.keras.Model:
        """Build the pattern recognition neural network"""
        model = Sequential()

        # Convolutional layers for pattern detection
        model.add(Conv1D(
            filters=self.hyperparameters['conv_filters'][0],
            kernel_size=self.hyperparameters['conv_kernel_size'],
            activation='relu',
            input_shape=(sequence_length, n_features)
        ))
        model.add(BatchNormalization())
        model.add(MaxPooling1D(pool_size=self.hyperparameters['pool_size']))
        model.add(Dropout(self.hyperparameters['dropout_rate']))

        model.add(Conv1D(
            filters=self.hyperparameters['conv_filters'][1],
            kernel_size=self.hyperparameters['conv_kernel_size'],
            activation='relu'
        ))
        model.add(BatchNormalization())
        model.add(MaxPooling1D(pool_size=self.hyperparameters['pool_size']))
        model.add(Dropout(self.hyperparameters['dropout_rate']))

        model.add(Conv1D(
            filters=self.hyperparameters['conv_filters'][2],
            kernel_size=self.hyperparameters['conv_kernel_size'],
            activation='relu'
        ))
        model.add(BatchNormalization())

        # LSTM layer for sequence understanding
        model.add(LSTM(self.hyperparameters['lstm_units'], return_sequences=False))
        model.add(Dropout(self.hyperparameters['dropout_rate']))

        # Dense layers for classification
        model.add(Dense(self.hyperparameters['dense_units'], activation='relu'))
        model.add(Dropout(self.hyperparameters['dropout_rate']))

        # Output layer
        model.add(Dense(len(self.pattern_types), activation='softmax'))

        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=self.hyperparameters['learning_rate']),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    def _prepare_pattern_data(self, data: pd.DataFrame, is_training: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for pattern recognition"""
        try:
            # Define features for pattern recognition
            feature_cols = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']

            # Filter available columns
            available_features = [col for col in feature_cols if col in data.columns]
            if not available_features:
                self.logger.error("No required features found in data")
                return None, None

            # For this implementation, we'll create synthetic pattern labels
            # In practice, you would have labeled pattern data
            pattern_labels = self._generate_synthetic_patterns(data)

            if len(pattern_labels) == 0:
                self.logger.error("No pattern labels generated")
                return None, None

            # Prepare feature sequences
            feature_data = data[available_features].values

            if is_training:
                # Fit scaler and label encoder
                scaled_features = self.scaler.fit_transform(feature_data)
                self.label_encoder.fit(pattern_labels)
                self.feature_columns = available_features
            else:
                # Use fitted scalers
                scaled_features = self.scaler.transform(feature_data)

            # Create sequences
            X, y = [], []
            for i in range(self.pattern_length, len(scaled_features)):
                if i < len(pattern_labels):
                    X.append(scaled_features[i-self.pattern_length:i])
                    y.append(pattern_labels[i])

            X = np.array(X)
            y = self.label_encoder.transform(y)

            # Convert to categorical
            y_categorical = tf.keras.utils.to_categorical(y, num_classes=len(self.pattern_types))

            return X, y_categorical

        except Exception as e:
            self.logger.error(f"Error preparing pattern data: {e}")
            return None, None

    def _prepare_pattern_sequences(self, data: pd.DataFrame) -> np.ndarray:
        """Prepare sequences for pattern recognition"""
        try:
            # Use only the features from training
            feature_data = data[self.feature_columns].values

            # Scale features
            scaled_features = self.scaler.transform(feature_data)

            # Create sequences
            if len(scaled_features) < self.pattern_length:
                self.logger.warning(f"Insufficient data for pattern recognition. Need {self.pattern_length}, got {len(scaled_features)}")
                return None

            # Take the last sequence
            X = scaled_features[-self.pattern_length:].reshape(1, self.pattern_length, -1)

            return X

        except Exception as e:
            self.logger.error(f"Error preparing pattern sequences: {e}")
            return None

    def _generate_synthetic_patterns(self, data: pd.DataFrame) -> List[str]:
        """Generate synthetic pattern labels for training (placeholder)"""
        # This is a simplified pattern detection for demonstration
        # In practice, you would have labeled data or more sophisticated pattern detection

        patterns = []
        close_prices = data['close_price'].values

        for i in range(len(close_prices)):
            if i < 20:
                patterns.append('no_pattern')
                continue

            # Simple pattern detection based on price movements
            recent_prices = close_prices[i-20:i]
            price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]

            # Volatility measure
            volatility = np.std(recent_prices) / np.mean(recent_prices)

            if price_change > 0.05 and volatility > 0.02:
                patterns.append('breakout_up')
            elif price_change < -0.05 and volatility > 0.02:
                patterns.append('breakout_down')
            elif volatility < 0.01:
                patterns.append('consolidation')
            elif price_change > 0.02:
                patterns.append('bullish_flag')
            elif price_change < -0.02:
                patterns.append('bearish_flag')
            else:
                patterns.append('no_pattern')

        return patterns

    async def recognize_pattern_with_confidence(self, input_data: pd.DataFrame) -> Tuple[str, float]:
        """Recognize pattern with confidence score"""
        try:
            probabilities = await self.predict(input_data, return_probabilities=True)

            if len(probabilities) == 0:
                return 'no_pattern', 0.0

            # Get the most confident prediction
            max_prob_idx = np.argmax(probabilities[0])
            confidence = probabilities[0][max_prob_idx]
            pattern = self.pattern_types[max_prob_idx]

            return pattern, confidence

        except Exception as e:
            self.logger.error(f"Error recognizing pattern: {e}")
            return 'no_pattern', 0.0

    def get_supported_patterns(self) -> List[str]:
        """Get list of supported pattern types"""
        return self.pattern_types.copy()