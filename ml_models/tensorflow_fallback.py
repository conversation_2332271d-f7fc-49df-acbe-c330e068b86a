"""
TensorFlow Fallback System
Provides fallback implementations when TensorFlow is not available
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from typing import Dict, List, Tuple, Any, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

class TensorFlowFallbackError(Exception):
    """Exception raised when TensorFlow is not available"""
    pass

def check_tensorflow_available():
    """Check if TensorFlow is available and working"""
    try:
        import tensorflow as tf
        # Try to create a simple tensor to test if TF works
        test_tensor = tf.constant([1.0, 2.0, 3.0])
        return True
    except (ImportError, Exception):
        return False

class PyTorchLSTM(nn.Module):
    """PyTorch LSTM implementation as TensorFlow fallback"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int, dropout: float = 0.2):
        super().__init__()
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.output_size = output_size
        
        # Build LSTM layers
        self.lstm_layers = nn.ModuleList()
        prev_size = input_size
        
        for hidden_size in hidden_sizes:
            self.lstm_layers.append(nn.LSTM(prev_size, hidden_size, batch_first=True, dropout=dropout))
            prev_size = hidden_size
        
        # Output layer
        self.output_layer = nn.Linear(prev_size, output_size)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # x shape: (batch, sequence, features)
        for lstm in self.lstm_layers:
            x, _ = lstm(x)
            x = self.dropout(x)
        
        # Take the last output
        x = x[:, -1, :]
        x = self.output_layer(x)
        return x

class PyTorchCNN(nn.Module):
    """PyTorch CNN implementation for pattern recognition"""
    
    def __init__(self, input_channels: int, sequence_length: int, num_classes: int):
        super().__init__()
        
        self.conv1 = nn.Conv1d(input_channels, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv1d(64, 128, kernel_size=3, padding=1)
        
        self.pool = nn.MaxPool1d(2)
        self.dropout = nn.Dropout(0.3)
        self.batch_norm1 = nn.BatchNorm1d(32)
        self.batch_norm2 = nn.BatchNorm1d(64)
        self.batch_norm3 = nn.BatchNorm1d(128)
        
        # Calculate the size after convolutions
        conv_output_size = sequence_length // 8  # After 3 pooling operations
        
        self.fc1 = nn.Linear(128 * conv_output_size, 100)
        self.fc2 = nn.Linear(100, num_classes)
        
    def forward(self, x):
        # x shape: (batch, sequence, features) -> (batch, features, sequence)
        x = x.transpose(1, 2)
        
        x = torch.relu(self.batch_norm1(self.conv1(x)))
        x = self.pool(x)
        x = self.dropout(x)
        
        x = torch.relu(self.batch_norm2(self.conv2(x)))
        x = self.pool(x)
        x = self.dropout(x)
        
        x = torch.relu(self.batch_norm3(self.conv3(x)))
        x = self.pool(x)
        x = self.dropout(x)
        
        # Flatten
        x = x.view(x.size(0), -1)
        
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        
        return x

class FallbackLSTMPredictor:
    """Fallback LSTM predictor using PyTorch or scikit-learn"""
    
    def __init__(self, sequence_length: int = 60, prediction_horizon: int = 1):
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.scaler = MinMaxScaler()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.use_pytorch = torch.cuda.is_available() or True  # Prefer PyTorch
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def prepare_data(self, data: pd.DataFrame, target_column: str = 'close'):
        """Prepare data for training"""
        if target_column not in data.columns:
            raise ValueError(f"Target column '{target_column}' not found in data")
        
        # Scale the data
        scaled_data = self.scaler.fit_transform(data.select_dtypes(include=[np.number]))
        
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_data)):
            X.append(scaled_data[i-self.sequence_length:i])
            y.append(scaled_data[i, data.columns.get_loc(target_column)])
        
        return np.array(X), np.array(y)
    
    def train(self, X: np.ndarray, y: np.ndarray, epochs: int = 50, batch_size: int = 32):
        """Train the model"""
        if self.use_pytorch:
            return self._train_pytorch(X, y, epochs, batch_size)
        else:
            return self._train_sklearn(X, y)
    
    def _train_pytorch(self, X: np.ndarray, y: np.ndarray, epochs: int, batch_size: int):
        """Train using PyTorch"""
        try:
            # Convert to tensors
            X_tensor = torch.FloatTensor(X).to(self.device)
            y_tensor = torch.FloatTensor(y).to(self.device)
            
            # Create model
            input_size = X.shape[2]
            self.model = PyTorchLSTM(input_size, [50, 50, 25], self.prediction_horizon).to(self.device)
            
            # Training setup
            criterion = nn.MSELoss()
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            
            # Create data loader
            dataset = TensorDataset(X_tensor, y_tensor)
            dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
            
            # Training loop
            self.model.train()
            for epoch in range(epochs):
                total_loss = 0
                for batch_X, batch_y in dataloader:
                    optimizer.zero_grad()
                    outputs = self.model(batch_X).squeeze()
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    total_loss += loss.item()
                
                if epoch % 10 == 0:
                    self.logger.info(f"Epoch {epoch}, Loss: {total_loss/len(dataloader):.6f}")
            
            return {"status": "success", "model_type": "pytorch_lstm"}
            
        except Exception as e:
            self.logger.error(f"PyTorch training failed: {e}")
            return self._train_sklearn(X, y)
    
    def _train_sklearn(self, X: np.ndarray, y: np.ndarray):
        """Train using scikit-learn as final fallback"""
        try:
            # Flatten the sequences for sklearn
            X_flat = X.reshape(X.shape[0], -1)
            
            # Use ensemble of models
            self.model = {
                'rf': RandomForestRegressor(n_estimators=100, random_state=42),
                'gb': GradientBoostingRegressor(n_estimators=100, random_state=42),
                'lr': LinearRegression()
            }
            
            # Train all models
            for name, model in self.model.items():
                model.fit(X_flat, y)
                self.logger.info(f"Trained {name} model")
            
            return {"status": "success", "model_type": "sklearn_ensemble"}
            
        except Exception as e:
            self.logger.error(f"Sklearn training failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions"""
        if self.model is None:
            raise ValueError("Model not trained yet")
        
        if self.use_pytorch and isinstance(self.model, nn.Module):
            return self._predict_pytorch(X)
        else:
            return self._predict_sklearn(X)
    
    def _predict_pytorch(self, X: np.ndarray) -> np.ndarray:
        """Predict using PyTorch model"""
        self.model.eval()
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X).to(self.device)
            predictions = self.model(X_tensor).cpu().numpy()
        return predictions
    
    def _predict_sklearn(self, X: np.ndarray) -> np.ndarray:
        """Predict using sklearn ensemble"""
        X_flat = X.reshape(X.shape[0], -1)
        predictions = []
        
        for name, model in self.model.items():
            pred = model.predict(X_flat)
            predictions.append(pred)
        
        # Average ensemble predictions
        return np.mean(predictions, axis=0)

# Global flag to check TensorFlow availability
TENSORFLOW_AVAILABLE = check_tensorflow_available()

def get_fallback_message():
    """Get message about TensorFlow fallback"""
    if not TENSORFLOW_AVAILABLE:
        return ("TensorFlow not available or not working. Using PyTorch/scikit-learn fallback. "
                "This may affect some ML model performance but core functionality remains intact.")
    return "TensorFlow available and working."
