# AstroA Paper Trading System - Complete Command Guide

## 🎉 TensorFlow Issues Fixed!
Your ML models now use PyTorch and are fully functional without TensorFlow compatibility issues.

## 📋 Prerequisites Check

First, verify everything is working:
```bash
# Test ML models (should work now)
python -c "from ml_models import TENSORFLOW_AVAILABLE, LSTMPredictor; print('✅ ML System Ready!')"

# Test configuration
python run_paper_trading.py --test-config
```

## 🚀 Paper Trading Commands

### 1. Quick Demo (5 minutes)
```bash
python run_demo_paper_trading.py --duration 5
```

### 2. Standard Paper Trading Session (1 hour)
```bash
python run_paper_trading.py --duration 1
```

### 3. Enhanced Paper Trading (with full ML)
```bash
python run_enhanced_paper_trading.py --duration 2
```

### 4. Original Concept Trading (news-based)
```bash
python run_original_concept_trading.py --duration 1
```

### 5. Unlimited Session (run until stopped)
```bash
python run_paper_trading.py
# Press Ctrl+C to stop
```

## 🎯 Recommended Command Sequence

### For Testing (Start Here):
```bash
# 1. Verify system is working
python run_paper_trading.py --test-config

# 2. Run a 5-minute demo
python run_demo_paper_trading.py --duration 5

# 3. If demo works, run a longer session
python run_enhanced_paper_trading.py --duration 1
```

### For Full Trading:
```bash
# Run the enhanced paper trading with all features
python run_enhanced_paper_trading.py --duration 4
```

## 🔧 Advanced Options

### Custom Symbols:
```bash
python run_paper_trading.py --symbols BTCUSD ETHUSD SOLUSD --duration 2
```

### Specific Strategies:
```bash
python run_paper_trading.py --strategies news_sentiment ml_enhanced --duration 1
```

### Background Monitoring:
```bash
# Start paper trading in background
nohup python run_enhanced_paper_trading.py --duration 8 > trading.log 2>&1 &

# Monitor progress
tail -f trading.log

# Check status
python monitor_paper_trading.py
```

## 📊 Monitoring Commands

### Real-time Monitoring:
```bash
# Monitor paper trading performance
python monitor_paper_trading.py

# Check trading status
python check_trading_status.py

# View logs
tail -f logs/paper_trading_$(date +%Y%m%d).log
```

### Performance Analysis:
```bash
# Generate backtest visualization
python generate_backtest_visualization.py

# Run performance benchmarks
python scripts/performance_benchmarks.py
```

## 🛠️ Troubleshooting Commands

### If Issues Occur:
```bash
# Test all APIs
python test_all_apis.py

# Test data collection
python test_data_collection.py

# Test mathematical engine
python test_mathematical_engine.py

# Verify installation
python test_installation.py
```

### Database Issues:
```bash
# Setup database
python setup_db.sql

# Test database connection
python -c "from database.models import *; print('✅ Database OK')"
```

## 📈 Expected Output

When running paper trading, you should see:
```
🌟 Starting AstroA Paper Trading Session
==================================================
💰 Initial Cash: $100,000.00
📊 Max Positions: 10
🎯 Tradable Symbols: BTCUSD, ETHUSD, ADAUSD, SOLUSD, DOTUSD...
⚡ Update Interval: 60s
==================================================
📅 Session duration: 1 hours
🕐 Session will end at: [TIME]

✅ ML models imported successfully
📊 TensorFlow available: False
🔧 LSTM Predictor: LSTMPredictor (PyTorch-based)
🔍 Pattern Recognizer: PatternRecognizer (Fallback mode)

🚀 Starting live trading session...
```

## 🎯 Recommended First Run

```bash
# Start with this command for your first run:
python run_enhanced_paper_trading.py --duration 1

# This will:
# - Run for 1 hour
# - Use all available strategies
# - Include ML predictions (PyTorch-based)
# - Generate performance reports
# - Save results to data/ directory
```

## 📁 Output Files

Results will be saved to:
- `data/enhanced_paper_trading_report_[timestamp].json` - Trading results
- `logs/enhanced_paper_trading_[date].log` - Detailed logs
- `reports/` - Performance analysis reports

## 🔄 Continuous Operation

For continuous trading:
```bash
# Run indefinitely (stop with Ctrl+C)
python run_enhanced_paper_trading.py

# Or run for a full trading day (8 hours)
python run_enhanced_paper_trading.py --duration 8
```

Your system is now fully operational with PyTorch-based ML models! 🎉
